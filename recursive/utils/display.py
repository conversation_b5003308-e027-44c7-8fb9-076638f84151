#coding: utf8
from recursive.graph import NodeType, TaskStatus
import json
from recursive.utils.file_io import enum_to_json
from loguru import logger

# Information Manager
# save the web pages in seperate file
# how to process redo?
# pickle.dump()

# one save in a folder
# different version save in different file (name is version name)
# key:
# - node: qiantao graph
#     - nid
#     - node_graph_info
#     - task_info
#     - raw_plan
#     - node_type
#     - status
#     - inner_graph

# - other
    
    
# - graph:
#     - 


# key:
#   - graph:
#     - version_name:
#         - meta_info
#             - config_path
#             - redo_at
#             - start_time
#         - content
#   - other:


def gen_root2node_path(node):
    paths = [node]
    while node.node_graph_info["outer_node"] is not None:
        paths.append(node.node_graph_info["outer_node"])
        node = node.node_graph_info["outer_node"]
    return paths[::-1]
    
    
    

def display_graph(graph, fn=None):
    if len(graph.nid_list) == 0:
        return
    nodes = sorted(graph.node_list, key=lambda x: int(str(x.nid).split(".")[-1]))
    display_string = []
    markers = "===" * 10
    markers2 = "---" * 10
    sep = "*******" * 10
    
    # special process root node
    display_string.append(f"{sep}")
    if graph.outer_node.node_graph_info["root_node"] == graph.outer_node:
        display_string.append(f"RootNode")
        print(graph.outer_node.task_str())
        display_string.append(json.dumps(graph.outer_node.result, 
                                         ensure_ascii=False, 
                                         indent=4, 
                                         default=enum_to_json))
    
    display_string.append("{} Internel in Level {}, Node {} {}".format(
        markers2,
        graph.outer_node.node_graph_info["layer"],
        " |-> ".join(["{}:{}".format(node.nid, node.task_info["goal"]) for node in gen_root2node_path(graph.outer_node)]),
        markers2))
    
    display_string.append(f"{markers} Graph Node Info {markers}")
    for node in nodes:
        display_string.append(node.task_str())
    display_string.append(f"{markers} Node Result {markers}")
    for node in nodes:
        if node.node_type == NodeType.PLAN_NODE:
            display_string.append("{} <Node {}: {}> {}".format(
                markers, node.nid, node.task_info["goal"], markers
            ))
            display_string.append(json.dumps(node.result, ensure_ascii=False, indent=4))
            display_string.append("\n")
        elif node.node_type == NodeType.EXECUTE_NODE:
            display_string.append("{} <Level = {} Node {}: {}> {}".format(
                markers, node.node_graph_info["layer"], node.nid, node.task_info["goal"], markers
            ))
            display_string.append(json.dumps(node.result, ensure_ascii=False, indent=4))
            display_string.append("\n")
        else:
            raise NotImplementedError("Error Node type: {}".format(node.node_type))
        
    print("\n".join(display_string))
    if fn is not None:
        with open(fn, "a") as f:
            f.write("\n".join(display_string))
    
    # display the internal node
    for node in graph.topological_task_queue:
        display_graph(node.inner_graph)

def display_plan(graph, fn=None):
    
    display_string = []
    
    def inner(cur_graph, prefix_tab):
        nodes = sorted(cur_graph.node_list, key=lambda x: int(str(x.nid).split(".")[-1]))
        # Root Node    
        if cur_graph.outer_node.node_graph_info["root_node"] == cur_graph.outer_node:
            display_string.append("Root: {}".format(str(cur_graph.outer_node)))
        for node in nodes:
            # 打印每个Node，首先打印该Node，然后打印该Node的子节点
            # 区分一下plan node和execute node, execute node直接打印
            if (node.node_type == NodeType.EXECUTE_NODE) or \
            (len(node.topological_task_queue) == 1 and node.topological_task_queue[0].node_type == NodeType.EXECUTE_NODE):
                display_string.append("{}*{}".format(prefix_tab, str(node)))
            else:
                display_string.append("{}{}".format(prefix_tab, str(node)))
                inner(node.inner_graph, prefix_tab+"\t")
    
    inner(graph, "\t")
    logger.info("Full Graph\n{}".format("\n".join(display_string)))