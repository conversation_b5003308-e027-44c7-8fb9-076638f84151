[metadata]
name = recursive
version = 0.0.2
description = A short description of your package
# long_description = file: README.md
# long_description_content_type = text/markdown

[options]
packages = find:
python_requires = >=3.6
install_requires = 
    class_registry
    griffe
    duckduckgo_search
    loguru
    langchain_text_splitters
    trafilatura
    overrides>=7.4.0
    tqdm

[options.extras_require]
dev =
    pytest
    gradio

[isort]
line_length = 100
multi_line_output = 3
balanced_wrapping = True
known_standard_library = setuptools
known_third_party = tqdm,loguru,tabulate,psutil
KNOWN_DATA_PROCESSING = numpy,scipy,PIL,matplotlib
KNOWN_DEEP_LEARNING = torch,torchvision,caffe2,onnx,apex,timm,thop,torch2trt,tensorrt,openvino,onnxruntime
KNOWN_MYSELF = gendata
sections = FUTURE,STDLIB,THIRDPARTY,DATA_PROCESSING,DEEP_LEARNING,MYSE<PERSON>,FIRSTPARTY,LOCALFOLDER
no_lines_before=STDLIB,THIRDPARTY
default_section = FIRSTPARTY

[flake8]
max-line-length = 100
max-complexity = 18
exclude = __init__.py
